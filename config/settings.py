"""
Configuration settings for the bank offer scraper
"""
import os
from typing import List, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Settings:
    """Application settings"""
    
    # Database configuration
    MONGODB_URL: str = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
    DATABASE_NAME: str = os.getenv("DATABASE_NAME", "bank_offers")
    COLLECTION_NAME: str = os.getenv("COLLECTION_NAME", "offers")
    
    # Scraping configuration
    REQUEST_TIMEOUT: int = int(os.getenv("REQUEST_TIMEOUT", "30"))
    MAX_RETRIES: int = int(os.getenv("MAX_RETRIES", "3"))
    DELAY_BETWEEN_REQUESTS: float = float(os.getenv("DELAY_BETWEEN_REQUESTS", "1.0"))
    
    # Browser configuration for Selenium
    HEADLESS_BROWSER: bool = os.getenv("HEADLESS_BROWSER", "True").lower() == "true"
    BROWSER_TIMEOUT: int = int(os.getenv("BROWSER_TIMEOUT", "30"))
    
    # Logging configuration
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "logs/scraper.log")
    
    # User agents for requests
    USER_AGENTS: List[str] = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    ]
    
    # Bank configurations
    BANKS: List[str] = [
        "BOC",
        "DFCC Bank",
        "People's Bank",
        "Commercial Bank",
        "Sampath Bank",
        "NTB",
        "NDB",
        "HSBC",
        "Seylan Bank",
        "HNB",
        "Union Bank",
        "Cargills Bank",
        "Pan Asia Bank",
        "LOLC"
    ]
    
    # Bank specific configurations
    BANK_CONFIGS: Dict[str, Dict[str, Any]] = {
        "Commercial Bank": {
            "base_url": "https://www.combank.lk",
            "offers_endpoint": "/offers",
            "scraping_method": "web_scraping",
            "requires_selenium": True
        },
        "Sampath Bank": {
            "base_url": "https://www.sampath.lk",
            "offers_endpoint": "/promotions",
            "scraping_method": "web_scraping",
            "requires_selenium": False
        },
        "DFCC Bank": {
            "base_url": "https://www.dfcc.lk",
            "offers_endpoint": "/offers",
            "scraping_method": "web_scraping",
            "requires_selenium": False
        },
        "BOC": {
            "base_url": "https://www.boc.lk",
            "offers_endpoint": "/promotions",
            "scraping_method": "web_scraping",
            "requires_selenium": True
        },
        "HNB": {
            "base_url": "https://www.hnb.lk",
            "offers_endpoint": "/offers",
            "scraping_method": "web_scraping",
            "requires_selenium": False
        }
    }


# Create settings instance
settings = Settings()
