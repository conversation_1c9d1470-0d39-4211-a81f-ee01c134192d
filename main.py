"""
Main entry point for the bank offer scraper
"""
import argparse
import sys
from typing import List, Optional
from datetime import datetime

from loguru import logger

from config import settings
from utils import db_manager, log_scraping_stats
from scrapers import get_scraper, get_available_banks, SCRAPERS
from models import ScrapingResult


def setup_database() -> bool:
    """Setup database connection"""
    if not db_manager.connect():
        logger.error("Failed to connect to database")
        return False
    return True


def scrape_bank(bank_name: str) -> ScrapingResult:
    """Scrape offers for a specific bank"""
    logger.info(f"Starting scraping for {bank_name}")
    
    try:
        scraper = get_scraper(bank_name)
        result = scraper.run()
        
        if result.success and result.offers:
            # Save offers to database
            new_offers_count = db_manager.insert_offers(result.offers)
            
            # Deactivate old offers that are no longer available
            deactivated_count = db_manager.deactivate_old_offers(bank_name, result.offers)
            
            log_scraping_stats(
                bank=bank_name,
                total_offers=result.offers_count,
                new_offers=new_offers_count,
                errors=0 if result.success else 1
            )
            
            logger.info(f"Successfully processed {bank_name}: {new_offers_count} new offers, {deactivated_count} deactivated")
        
        else:
            logger.warning(f"No offers found for {bank_name}: {result.error_message}")
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to scrape {bank_name}: {e}")
        return ScrapingResult(
            bank=bank_name,
            success=False,
            error_message=str(e)
        )


def scrape_all_banks() -> List[ScrapingResult]:
    """Scrape offers from all available banks"""
    results = []
    available_banks = get_available_banks()
    
    logger.info(f"Starting scraping for {len(available_banks)} banks")
    
    for bank_name in available_banks:
        try:
            result = scrape_bank(bank_name)
            results.append(result)
            
            # Add delay between banks to be respectful
            import time
            time.sleep(2)
            
        except Exception as e:
            logger.error(f"Error scraping {bank_name}: {e}")
            results.append(ScrapingResult(
                bank=bank_name,
                success=False,
                error_message=str(e)
            ))
    
    return results


def print_summary(results: List[ScrapingResult]):
    """Print summary of scraping results"""
    print("\n" + "="*60)
    print("SCRAPING SUMMARY")
    print("="*60)
    
    total_offers = 0
    successful_banks = 0
    failed_banks = 0
    
    for result in results:
        status = "✓" if result.success else "✗"
        print(f"{status} {result.bank}: {result.offers_count} offers")
        
        if result.success:
            successful_banks += 1
            total_offers += result.offers_count
        else:
            failed_banks += 1
            if result.error_message:
                print(f"   Error: {result.error_message}")
    
    print("-"*60)
    print(f"Total offers collected: {total_offers}")
    print(f"Successful banks: {successful_banks}")
    print(f"Failed banks: {failed_banks}")
    print(f"Success rate: {(successful_banks / len(results) * 100):.1f}%")
    print("="*60)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Bank Offer Scraper")
    parser.add_argument(
        "--bank",
        type=str,
        help="Specific bank to scrape (e.g., 'Commercial Bank')"
    )
    parser.add_argument(
        "--list-banks",
        action="store_true",
        help="List all available banks"
    )
    parser.add_argument(
        "--all",
        action="store_true",
        help="Scrape all available banks"
    )
    
    args = parser.parse_args()
    
    # List available banks
    if args.list_banks:
        print("Available banks:")
        for bank in get_available_banks():
            print(f"  - {bank}")
        return
    
    # Setup database
    if not setup_database():
        sys.exit(1)
    
    try:
        results = []
        
        if args.bank:
            # Scrape specific bank
            if args.bank not in get_available_banks():
                logger.error(f"Bank '{args.bank}' not available. Use --list-banks to see available banks.")
                sys.exit(1)
            
            result = scrape_bank(args.bank)
            results.append(result)
            
        elif args.all:
            # Scrape all banks
            results = scrape_all_banks()
            
        else:
            # Default: scrape all banks
            results = scrape_all_banks()
        
        # Print summary
        print_summary(results)
        
        # Check if any scraping failed
        failed_count = sum(1 for r in results if not r.success)
        if failed_count > 0:
            logger.warning(f"{failed_count} banks failed to scrape")
            sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
        sys.exit(0)
    
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
    
    finally:
        # Cleanup database connection
        db_manager.disconnect()


if __name__ == "__main__":
    main()
