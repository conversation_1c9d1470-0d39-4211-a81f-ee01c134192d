"""
Test script to verify the setup
"""
import sys
import os

def test_imports():
    """Test if all modules can be imported"""
    print("Testing imports...")
    
    try:
        from config import settings
        print("✓ Config imported successfully")
    except ImportError as e:
        print(f"✗ Config import failed: {e}")
        return False
    
    try:
        from models import BankOffer, ScrapingResult
        print("✓ Models imported successfully")
    except ImportError as e:
        print(f"✗ Models import failed: {e}")
        return False
    
    try:
        from utils import db_manager
        print("✓ Utils imported successfully")
    except ImportError as e:
        print(f"✗ Utils import failed: {e}")
        return False
    
    try:
        from scrapers import get_available_banks, get_scraper
        print("✓ Scrapers imported successfully")
    except ImportError as e:
        print(f"✗ Scrapers import failed: {e}")
        return False
    
    return True


def test_configuration():
    """Test configuration"""
    print("\nTesting configuration...")
    
    from config import settings
    
    print(f"✓ Database URL: {settings.MONGODB_URL}")
    print(f"✓ Database Name: {settings.DATABASE_NAME}")
    print(f"✓ Collection Name: {settings.COLLECTION_NAME}")
    print(f"✓ Request Timeout: {settings.REQUEST_TIMEOUT}")
    print(f"✓ Available Banks: {len(settings.BANKS)}")
    
    return True


def test_scrapers():
    """Test scraper availability"""
    print("\nTesting scrapers...")
    
    from scrapers import get_available_banks, get_scraper
    
    available_banks = get_available_banks()
    print(f"✓ Available scrapers: {len(available_banks)}")
    
    for bank in available_banks:
        try:
            scraper = get_scraper(bank)
            print(f"  ✓ {bank}: {scraper.__class__.__name__}")
        except Exception as e:
            print(f"  ✗ {bank}: {e}")
            return False
    
    return True


def test_data_model():
    """Test data model creation"""
    print("\nTesting data model...")
    
    from models import BankOffer
    from datetime import datetime
    
    try:
        # Create a sample offer
        offer = BankOffer(
            bank="Test Bank",
            merchant="Test Merchant",
            discount="20% OFF",
            description="Test offer description",
            valid_until="31 Dec 2025",
            category="Shopping"
        )
        
        print("✓ BankOffer created successfully")
        print(f"  Bank: {offer.bank}")
        print(f"  Merchant: {offer.merchant}")
        print(f"  Discount: {offer.discount}")
        print(f"  Category: {offer.category}")
        
        # Test conversion to dict
        offer_dict = offer.to_dict()
        print("✓ Offer converted to dict successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Data model test failed: {e}")
        return False


def test_database_connection():
    """Test database connection (optional)"""
    print("\nTesting database connection...")
    
    from utils import db_manager
    
    try:
        # Try to connect
        if db_manager.connect():
            print("✓ Database connection successful")
            db_manager.disconnect()
            return True
        else:
            print("✗ Database connection failed")
            return False
            
    except Exception as e:
        print(f"⚠ Database connection test skipped: {e}")
        print("  (This is normal if MongoDB is not running)")
        return True


def test_helper_functions():
    """Test helper functions"""
    print("\nTesting helper functions...")
    
    from utils import (
        clean_text,
        extract_discount_percentage,
        categorize_offer,
        parse_date
    )
    
    # Test text cleaning
    dirty_text = "  This is   a test   text  \n\n  "
    clean = clean_text(dirty_text)
    print(f"✓ Text cleaning: '{dirty_text}' -> '{clean}'")
    
    # Test discount extraction
    discount_text = "Get 25% OFF on all items"
    discount = extract_discount_percentage(discount_text)
    print(f"✓ Discount extraction: '{discount_text}' -> '{discount}'")
    
    # Test categorization
    category = categorize_offer("Pizza Hut", "20% off on pizza orders")
    print(f"✓ Categorization: 'Pizza Hut' -> '{category}'")
    
    # Test date parsing
    date_str = "31st December 2025"
    parsed_date = parse_date(date_str)
    print(f"✓ Date parsing: '{date_str}' -> '{parsed_date}'")
    
    return True


def main():
    """Run all tests"""
    print("Bank Offer Scraper - Setup Test")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_configuration,
        test_scrapers,
        test_data_model,
        test_helper_functions,
        test_database_connection,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ {test.__name__} failed")
        except Exception as e:
            print(f"✗ {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Setup is complete.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
