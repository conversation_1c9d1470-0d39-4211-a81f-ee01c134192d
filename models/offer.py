"""
Data models for bank offers
"""
from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from bson import ObjectId


class PyObjectId(ObjectId):
    """Custom ObjectId type for Pydantic"""
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __modify_schema__(cls, field_schema):
        field_schema.update(type="string")


class BankOffer(BaseModel):
    """Bank offer data model"""
    
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    bank: str = Field(..., description="Name of the bank")
    merchant: str = Field(..., description="Merchant or business name")
    discount: str = Field(..., description="Discount description (e.g., '20% OFF')")
    description: str = Field(..., description="Detailed description of the offer")
    valid_until: str = Field(..., alias="validUntil", description="Offer validity date")
    category: str = Field(..., description="Offer category")
    image_url: Optional[str] = Field(None, alias="imageUrl", description="URL of the offer image")
    is_active: bool = Field(True, alias="isActive", description="Whether the offer is active")
    terms: str = Field("", description="Terms and conditions")
    minimum_spend: Optional[float] = Field(None, alias="minimumSpend", description="Minimum spend amount")
    maximum_discount: Optional[float] = Field(None, alias="maximumDiscount", description="Maximum discount amount")
    created_at: datetime = Field(default_factory=datetime.utcnow, alias="createdAt")
    updated_at: datetime = Field(default_factory=datetime.utcnow, alias="updatedAt")
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}
        schema_extra = {
            "example": {
                "bank": "Commercial Bank",
                "merchant": "Hilton Colombo",
                "discount": "20% OFF",
                "description": "20% off on total bill for Commercial Bank credit card holders",
                "validUntil": "31 Dec 2025",
                "category": "Travel & Leisure",
                "imageUrl": "https://example.com/hilton-colombo.jpg",
                "isActive": True,
                "terms": "",
                "minimumSpend": 5000,
                "maximumDiscount": 10000
            }
        }
    
    @validator('bank')
    def validate_bank(cls, v):
        """Validate bank name"""
        if not v or len(v.strip()) == 0:
            raise ValueError('Bank name cannot be empty')
        return v.strip()
    
    @validator('merchant')
    def validate_merchant(cls, v):
        """Validate merchant name"""
        if not v or len(v.strip()) == 0:
            raise ValueError('Merchant name cannot be empty')
        return v.strip()
    
    @validator('category')
    def validate_category(cls, v):
        """Validate category"""
        valid_categories = [
            "Travel & Leisure",
            "Dining",
            "Shopping",
            "Fuel",
            "Utilities",
            "Healthcare",
            "Entertainment",
            "Online Shopping",
            "Grocery",
            "Fashion",
            "Electronics",
            "Other"
        ]
        if v not in valid_categories:
            return "Other"
        return v
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for MongoDB insertion"""
        data = self.dict(by_alias=True, exclude_unset=True)
        if "_id" in data and data["_id"] is None:
            del data["_id"]
        return data


class ScrapingResult(BaseModel):
    """Result of a scraping operation"""
    
    bank: str
    success: bool
    offers_count: int = 0
    offers: list[BankOffer] = []
    error_message: Optional[str] = None
    scraping_time: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        arbitrary_types_allowed = True
