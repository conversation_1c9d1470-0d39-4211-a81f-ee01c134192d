// MongoDB initialization script
db = db.getSiblingDB('bank_offers');

// Create the offers collection
db.createCollection('offers');

// Create indexes for better performance
db.offers.createIndex({ "bank": 1 });
db.offers.createIndex({ "merchant": 1 });
db.offers.createIndex({ "category": 1 });
db.offers.createIndex({ "isActive": 1 });
db.offers.createIndex({ "createdAt": 1 });
db.offers.createIndex({ "updatedAt": 1 });

// Create compound index for uniqueness check
db.offers.createIndex({ 
    "bank": 1, 
    "merchant": 1, 
    "description": 1 
}, { 
    name: "unique_offer_index" 
});

print("Database and indexes created successfully!");
