"""
Setup script for the bank offer scraper
"""
import os
import sys
import subprocess
from pathlib import Path


def create_directories():
    """Create necessary directories"""
    directories = [
        "logs",
        "data",
        "temp"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")


def create_env_file():
    """Create .env file from template if it doesn't exist"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        env_file.write_text(env_example.read_text())
        print("✓ Created .env file from template")
        print("  Please edit .env file with your configuration")
    elif env_file.exists():
        print("✓ .env file already exists")
    else:
        print("⚠ .env.example not found")


def install_dependencies():
    """Install Python dependencies"""
    print("Installing Python dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✓ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install dependencies: {e}")
        return False


def check_python_version():
    """Check Python version"""
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("⚠ Python 3.8+ is recommended")
        return False
    else:
        print("✓ Python version is compatible")
        return True


def check_chrome():
    """Check if Chrome is installed"""
    try:
        # Try to find Chrome executable
        import shutil
        chrome_paths = [
            "google-chrome",
            "chrome",
            "chromium",
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
            "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe"
        ]
        
        for path in chrome_paths:
            if shutil.which(path) or os.path.exists(path):
                print("✓ Chrome browser found")
                return True
        
        print("⚠ Chrome browser not found")
        print("  Please install Google Chrome for Selenium functionality")
        return False
        
    except Exception as e:
        print(f"⚠ Could not check Chrome installation: {e}")
        return False


def run_tests():
    """Run setup tests"""
    print("\nRunning setup tests...")
    
    try:
        result = subprocess.run([sys.executable, "test_setup.py"], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"✗ Failed to run tests: {e}")
        return False


def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "=" * 50)
    print("SETUP COMPLETE!")
    print("=" * 50)
    print("\nNext steps:")
    print("1. Edit .env file with your MongoDB configuration")
    print("2. Start MongoDB server")
    print("3. Test the setup:")
    print("   python test_setup.py")
    print("4. List available banks:")
    print("   python main.py --list-banks")
    print("5. Run a test scrape:")
    print("   python main.py --bank 'Commercial Bank'")
    print("6. Run all scrapers:")
    print("   python main.py --all")
    print("\nFor more information, see README.md")


def main():
    """Main setup function"""
    print("Bank Offer Scraper - Setup")
    print("=" * 30)
    
    # Check Python version
    if not check_python_version():
        print("Please upgrade to Python 3.8 or higher")
        return 1
    
    # Create directories
    print("\nCreating directories...")
    create_directories()
    
    # Create .env file
    print("\nSetting up configuration...")
    create_env_file()
    
    # Check Chrome
    print("\nChecking browser...")
    check_chrome()
    
    # Install dependencies
    print("\nInstalling dependencies...")
    if not install_dependencies():
        print("Please install dependencies manually: pip install -r requirements.txt")
        return 1
    
    # Run tests
    if not run_tests():
        print("Some tests failed. Please check the output above.")
        return 1
    
    # Print next steps
    print_next_steps()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
