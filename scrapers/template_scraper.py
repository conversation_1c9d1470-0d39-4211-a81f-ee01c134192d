"""
Template scraper for new banks
Copy this file and modify for each new bank
"""
from typing import List, Dict, Any
from loguru import logger

from .base_scraper import WebScrapingScraper
from models import ScrapingResult, BankOffer


class TemplateBankScraper(WebScrapingScraper):
    """Template scraper - copy and modify for new banks"""
    
    def __init__(self, bank_name: str, base_url: str, offers_endpoint: str = "/offers"):
        super().__init__(bank_name)
        self.base_url = base_url
        self.offers_url = f"{self.base_url}{offers_endpoint}"
    
    def scrape_offers(self) -> ScrapingResult:
        """Scrape offers from bank website"""
        offers = []
        
        try:
            # Step 1: Try to load the offers page
            soup = self.scrape_with_requests(self.offers_url)
            
            # If requests fail, try Selenium (for dynamic content)
            if not soup:
                soup = self.scrape_with_selenium(self.offers_url)
            
            if not soup:
                return ScrapingResult(
                    bank=self.bank_name,
                    success=False,
                    error_message="Failed to load offers page"
                )
            
            # Step 2: Find offer containers
            # Modify these selectors based on the actual website structure
            offer_containers = soup.find_all('div', class_=[
                'offer-card', 'promotion-card', 'deal-card',
                'offer-item', 'promo-item', 'card'
            ])
            
            # If no containers found, try alternative approach
            if not offer_containers:
                offer_containers = self.find_alternative_containers(soup)
            
            logger.info(f"Found {len(offer_containers)} potential offer containers for {self.bank_name}")
            
            # Step 3: Extract data from each container
            for container in offer_containers:
                offer_data = self.extract_offer_data(container)
                if offer_data:
                    offer = self.create_offer(offer_data)
                    if offer:
                        offers.append(offer)
            
            return ScrapingResult(
                bank=self.bank_name,
                success=True,
                offers_count=len(offers),
                offers=offers
            )
            
        except Exception as e:
            logger.error(f"Error scraping {self.bank_name} offers: {e}")
            return ScrapingResult(
                bank=self.bank_name,
                success=False,
                error_message=str(e)
            )
    
    def find_alternative_containers(self, soup) -> List:
        """Find offer containers using alternative methods"""
        containers = []
        
        # Method 1: Look for containers with offer-related text
        all_divs = soup.find_all('div')
        for div in all_divs:
            text = div.get_text().lower()
            if any(keyword in text for keyword in ['offer', 'discount', '%', 'off', 'promotion']):
                # Check if it's a reasonable size (not too small or too large)
                if 50 < len(text) < 1000:
                    containers.append(div)
        
        # Method 2: Look for specific patterns
        # Add more methods based on the website structure
        
        return containers[:20]  # Limit to avoid processing too many
    
    def extract_offer_data(self, container) -> Dict[str, Any]:
        """Extract offer data from HTML container"""
        try:
            offer_data = {}
            
            # Extract merchant/title
            # Try multiple selectors for title/merchant
            title_selectors = [
                ['h1', 'h2', 'h3', 'h4', 'h5'],
                [{'class': ['title', 'heading', 'merchant', 'name']}],
                [{'class': ['card-title', 'offer-title']}]
            ]
            
            for selector_group in title_selectors:
                if isinstance(selector_group[0], str):
                    title_elem = container.find(selector_group)
                else:
                    title_elem = container.find(['div', 'span', 'h1', 'h2', 'h3'], selector_group[0])
                
                if title_elem:
                    offer_data['merchant'] = title_elem.get_text(strip=True)
                    break
            
            # Extract discount information
            discount_selectors = [
                [{'class': ['discount', 'percentage', 'offer-value']}],
                [{'class': ['badge', 'tag', 'label']}],
                [{'style': lambda x: x and 'color' in x}]  # Often highlighted
            ]
            
            for selector in discount_selectors:
                discount_elem = container.find(['span', 'div', 'strong'], selector[0])
                if discount_elem:
                    text = discount_elem.get_text(strip=True)
                    if '%' in text or 'off' in text.lower() or 'discount' in text.lower():
                        offer_data['discount'] = text
                        break
            
            # If no specific discount found, look in all text
            if not offer_data.get('discount'):
                all_text = container.get_text()
                import re
                discount_match = re.search(r'(\d+%\s*(?:off|OFF|discount|DISCOUNT)?)', all_text)
                if discount_match:
                    offer_data['discount'] = discount_match.group(1)
            
            # Extract description
            desc_selectors = [
                [{'class': ['description', 'content', 'details']}],
                [{'class': ['card-text', 'offer-description']}]
            ]
            
            for selector in desc_selectors:
                desc_elem = container.find(['p', 'div'], selector[0])
                if desc_elem:
                    offer_data['description'] = desc_elem.get_text(strip=True)
                    break
            
            # If no description found, use container text
            if not offer_data.get('description'):
                all_text = container.get_text(strip=True)
                # Remove title from description
                if offer_data.get('merchant'):
                    all_text = all_text.replace(offer_data['merchant'], '').strip()
                # Limit length
                offer_data['description'] = all_text[:300] + "..." if len(all_text) > 300 else all_text
            
            # Extract validity period
            validity_keywords = ['valid until', 'expires on', 'till', 'validity', 'expiry']
            for elem in container.find_all(['span', 'div', 'p', 'small']):
                text = elem.get_text().lower()
                if any(keyword in text for keyword in validity_keywords):
                    offer_data['valid_until'] = elem.get_text(strip=True)
                    break
            
            # Extract image
            img_elem = container.find('img')
            if img_elem:
                img_src = img_elem.get('src') or img_elem.get('data-src') or img_elem.get('data-lazy')
                if img_src:
                    if img_src.startswith('/'):
                        img_src = self.base_url + img_src
                    elif img_src.startswith('//'):
                        img_src = 'https:' + img_src
                    offer_data['image_url'] = img_src
            
            # Extract terms and conditions
            terms_selectors = [
                [{'class': ['terms', 'conditions', 'fine-print', 'tnc']}],
                [{'class': ['small-text', 'disclaimer']}]
            ]
            
            for selector in terms_selectors:
                terms_elem = container.find(['div', 'p', 'small'], selector[0])
                if terms_elem:
                    offer_data['terms'] = terms_elem.get_text(strip=True)
                    break
            
            # Look for minimum spend information
            text_content = container.get_text().lower()
            if any(phrase in text_content for phrase in ['minimum spend', 'min purchase', 'minimum amount']):
                for elem in container.find_all(['span', 'div', 'p']):
                    elem_text = elem.get_text()
                    if 'minimum' in elem_text.lower() and any(currency in elem_text.lower() for currency in ['rs', 'lkr', 'rupee']):
                        offer_data['minimum_spend'] = elem_text.strip()
                        break
            
            # Validate minimum required data
            if not offer_data.get('merchant') and not offer_data.get('description'):
                return None
            
            # Set defaults for missing required fields
            if not offer_data.get('merchant'):
                offer_data['merchant'] = f"{self.bank_name} Partner"
            
            if not offer_data.get('description'):
                offer_data['description'] = f"Special offer for {self.bank_name} customers"
            
            if not offer_data.get('discount'):
                offer_data['discount'] = "Special Offer"
            
            return offer_data
            
        except Exception as e:
            logger.error(f"Error extracting offer data for {self.bank_name}: {e}")
            return None


# Example implementations for specific banks
class BOCScraper(TemplateBankScraper):
    """Bank of Ceylon scraper"""
    
    def __init__(self):
        super().__init__("BOC", "https://www.boc.lk", "/promotions")


class PeoplesBankScraper(TemplateBankScraper):
    """People's Bank scraper"""
    
    def __init__(self):
        super().__init__("People's Bank", "https://www.peoplesbank.lk", "/offers")


class NTBScraper(TemplateBankScraper):
    """Nations Trust Bank scraper"""
    
    def __init__(self):
        super().__init__("NTB", "https://www.ntb.lk", "/promotions")


class NDBScraper(TemplateBankScraper):
    """National Development Bank scraper"""
    
    def __init__(self):
        super().__init__("NDB", "https://www.ndb.lk", "/offers")


class HSBCScraper(TemplateBankScraper):
    """HSBC Bank scraper"""
    
    def __init__(self):
        super().__init__("HSBC", "https://www.hsbc.lk", "/credit-cards/offers")


class SeylanBankScraper(TemplateBankScraper):
    """Seylan Bank scraper"""
    
    def __init__(self):
        super().__init__("Seylan Bank", "https://www.seylan.lk", "/promotions")


class HNBScraper(TemplateBankScraper):
    """Hatton National Bank scraper"""
    
    def __init__(self):
        super().__init__("HNB", "https://www.hnb.lk", "/offers")


class UnionBankScraper(TemplateBankScraper):
    """Union Bank scraper"""
    
    def __init__(self):
        super().__init__("Union Bank", "https://www.unionb.com", "/promotions")


class CargillsBankScraper(TemplateBankScraper):
    """Cargills Bank scraper"""
    
    def __init__(self):
        super().__init__("Cargills Bank", "https://www.cargillsbank.com", "/offers")


class PanAsiaBankScraper(TemplateBankScraper):
    """Pan Asia Bank scraper"""
    
    def __init__(self):
        super().__init__("Pan Asia Bank", "https://www.panasiabank.com", "/promotions")


class LOLCScraper(TemplateBankScraper):
    """LOLC Bank scraper"""
    
    def __init__(self):
        super().__init__("LOLC", "https://www.lolc.lk", "/offers")
