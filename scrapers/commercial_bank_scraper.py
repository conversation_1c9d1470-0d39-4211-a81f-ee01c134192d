"""
Commercial Bank scraper
"""
from typing import List, Dict, Any
from loguru import logger

from .base_scraper import WebScrapingScraper
from models import ScrapingResult, BankOffer


class CommercialBankScraper(WebScrapingScraper):
    """Scraper for Commercial Bank offers"""
    
    def __init__(self):
        super().__init__("Commercial Bank")
        self.base_url = "https://www.combank.lk"
        self.offers_url = f"{self.base_url}/offers"
    
    def scrape_offers(self) -> ScrapingResult:
        """Scrape offers from Commercial Bank website"""
        offers = []
        
        try:
            # Use Selenium for dynamic content
            soup = self.scrape_with_selenium(self.offers_url)
            if not soup:
                return ScrapingResult(
                    bank=self.bank_name,
                    success=False,
                    error_message="Failed to load offers page"
                )
            
            # Find offer containers (adjust selectors based on actual website structure)
            offer_containers = soup.find_all('div', class_=['offer-card', 'promotion-item', 'offer-container'])
            
            if not offer_containers:
                # Try alternative selectors
                offer_containers = soup.find_all('div', class_=['card', 'item'])
                offer_containers = [container for container in offer_containers 
                                 if any(keyword in container.get_text().lower() 
                                       for keyword in ['offer', 'discount', 'promotion', '%'])]
            
            logger.info(f"Found {len(offer_containers)} potential offer containers")
            
            for container in offer_containers:
                offer_data = self.extract_offer_data(container)
                if offer_data:
                    offer = self.create_offer(offer_data)
                    if offer:
                        offers.append(offer)
            
            return ScrapingResult(
                bank=self.bank_name,
                success=True,
                offers_count=len(offers),
                offers=offers
            )
            
        except Exception as e:
            logger.error(f"Error scraping Commercial Bank offers: {e}")
            return ScrapingResult(
                bank=self.bank_name,
                success=False,
                error_message=str(e)
            )
    
    def extract_offer_data(self, container) -> Dict[str, Any]:
        """Extract offer data from HTML container"""
        try:
            offer_data = {}
            
            # Extract merchant name
            merchant_elem = container.find(['h3', 'h4', 'h5'], class_=['title', 'merchant', 'name'])
            if not merchant_elem:
                merchant_elem = container.find(['h3', 'h4', 'h5'])
            if merchant_elem:
                offer_data['merchant'] = merchant_elem.get_text(strip=True)
            
            # Extract discount/offer text
            discount_elem = container.find(['span', 'div', 'p'], class_=['discount', 'offer', 'percentage'])
            if not discount_elem:
                # Look for text containing % or "off"
                for elem in container.find_all(['span', 'div', 'p']):
                    text = elem.get_text().lower()
                    if '%' in text or 'off' in text or 'discount' in text:
                        discount_elem = elem
                        break
            
            if discount_elem:
                offer_data['discount'] = discount_elem.get_text(strip=True)
            
            # Extract description
            desc_elem = container.find(['p', 'div'], class_=['description', 'details', 'content'])
            if not desc_elem:
                # Get all text and use as description
                all_text = container.get_text(strip=True)
                offer_data['description'] = all_text[:200] + "..." if len(all_text) > 200 else all_text
            else:
                offer_data['description'] = desc_elem.get_text(strip=True)
            
            # Extract validity date
            date_elem = container.find(['span', 'div', 'p'], class_=['validity', 'date', 'expires'])
            if not date_elem:
                # Look for date patterns in text
                for elem in container.find_all(['span', 'div', 'p']):
                    text = elem.get_text()
                    if any(keyword in text.lower() for keyword in ['valid', 'expires', 'until', 'till']):
                        date_elem = elem
                        break
            
            if date_elem:
                offer_data['valid_until'] = date_elem.get_text(strip=True)
            
            # Extract image URL
            img_elem = container.find('img')
            if img_elem and img_elem.get('src'):
                img_url = img_elem['src']
                if img_url.startswith('/'):
                    img_url = self.base_url + img_url
                offer_data['image_url'] = img_url
            
            # Extract terms and conditions
            terms_elem = container.find(['div', 'p'], class_=['terms', 'conditions', 'tnc'])
            if terms_elem:
                offer_data['terms'] = terms_elem.get_text(strip=True)
            
            # Extract minimum spend if available
            for elem in container.find_all(['span', 'div', 'p']):
                text = elem.get_text().lower()
                if 'minimum' in text and ('spend' in text or 'purchase' in text):
                    offer_data['minimum_spend'] = elem.get_text(strip=True)
                    break
            
            # Extract maximum discount if available
            for elem in container.find_all(['span', 'div', 'p']):
                text = elem.get_text().lower()
                if 'maximum' in text and ('discount' in text or 'saving' in text):
                    offer_data['maximum_discount'] = elem.get_text(strip=True)
                    break
            
            # Ensure we have minimum required data
            if not offer_data.get('merchant') and not offer_data.get('description'):
                return None
            
            # Use description as merchant if merchant is missing
            if not offer_data.get('merchant'):
                offer_data['merchant'] = offer_data.get('description', 'Commercial Bank Offer')[:50]
            
            # Use merchant as description if description is missing
            if not offer_data.get('description'):
                offer_data['description'] = f"Special offer at {offer_data['merchant']}"
            
            # Set default discount if missing
            if not offer_data.get('discount'):
                offer_data['discount'] = "Special Offer"
            
            return offer_data
            
        except Exception as e:
            logger.error(f"Error extracting offer data: {e}")
            return None
