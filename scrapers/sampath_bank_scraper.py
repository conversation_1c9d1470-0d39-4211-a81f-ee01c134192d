"""
Sampath Bank scraper
"""
from typing import List, Dict, Any
from loguru import logger

from .base_scraper import WebScrapingScraper
from models import ScrapingResult, BankOffer


class SampathBankScraper(WebScrapingScraper):
    """Scraper for Sampath Bank offers"""
    
    def __init__(self):
        super().__init__("Sampath Bank")
        self.base_url = "https://www.sampath.lk"
        self.offers_url = f"{self.base_url}/promotions"
    
    def scrape_offers(self) -> ScrapingResult:
        """Scrape offers from Sampath Bank website"""
        offers = []
        
        try:
            # Try requests first (faster)
            soup = self.scrape_with_requests(self.offers_url)
            
            # If requests fail, try Selenium
            if not soup:
                soup = self.scrape_with_selenium(self.offers_url)
            
            if not soup:
                return ScrapingResult(
                    bank=self.bank_name,
                    success=False,
                    error_message="Failed to load offers page"
                )
            
            # Find offer containers
            offer_containers = soup.find_all('div', class_=['promotion-card', 'offer-item', 'promo-container'])
            
            if not offer_containers:
                # Try alternative selectors
                offer_containers = soup.find_all('div', class_=['card', 'item', 'box'])
                offer_containers = [container for container in offer_containers 
                                 if self.is_offer_container(container)]
            
            logger.info(f"Found {len(offer_containers)} potential offer containers")
            
            for container in offer_containers:
                offer_data = self.extract_offer_data(container)
                if offer_data:
                    offer = self.create_offer(offer_data)
                    if offer:
                        offers.append(offer)
            
            return ScrapingResult(
                bank=self.bank_name,
                success=True,
                offers_count=len(offers),
                offers=offers
            )
            
        except Exception as e:
            logger.error(f"Error scraping Sampath Bank offers: {e}")
            return ScrapingResult(
                bank=self.bank_name,
                success=False,
                error_message=str(e)
            )
    
    def is_offer_container(self, container) -> bool:
        """Check if container contains offer information"""
        text = container.get_text().lower()
        offer_keywords = ['offer', 'discount', 'promotion', '%', 'off', 'deal', 'save']
        return any(keyword in text for keyword in offer_keywords)
    
    def extract_offer_data(self, container) -> Dict[str, Any]:
        """Extract offer data from HTML container"""
        try:
            offer_data = {}
            
            # Extract merchant/title
            title_elem = container.find(['h1', 'h2', 'h3', 'h4'], class_=['title', 'heading', 'merchant'])
            if not title_elem:
                title_elem = container.find(['h1', 'h2', 'h3', 'h4'])
            if title_elem:
                offer_data['merchant'] = title_elem.get_text(strip=True)
            
            # Extract discount information
            discount_selectors = [
                {'class': ['discount', 'percentage', 'offer-value']},
                {'class': ['badge', 'tag']},
                {'style': lambda x: x and ('color' in x or 'font-weight' in x)}
            ]
            
            for selector in discount_selectors:
                discount_elem = container.find(['span', 'div', 'strong'], selector)
                if discount_elem:
                    text = discount_elem.get_text(strip=True)
                    if '%' in text or 'off' in text.lower():
                        offer_data['discount'] = text
                        break
            
            # Extract description
            desc_elem = container.find(['p', 'div'], class_=['description', 'content', 'details'])
            if desc_elem:
                offer_data['description'] = desc_elem.get_text(strip=True)
            else:
                # Use all text as description
                all_text = container.get_text(strip=True)
                # Remove title from description
                if offer_data.get('merchant'):
                    all_text = all_text.replace(offer_data['merchant'], '').strip()
                offer_data['description'] = all_text[:300] + "..." if len(all_text) > 300 else all_text
            
            # Extract validity period
            validity_patterns = ['valid until', 'expires on', 'till', 'validity']
            for elem in container.find_all(['span', 'div', 'p', 'small']):
                text = elem.get_text().lower()
                if any(pattern in text for pattern in validity_patterns):
                    offer_data['valid_until'] = elem.get_text(strip=True)
                    break
            
            # Extract image
            img_elem = container.find('img')
            if img_elem:
                img_src = img_elem.get('src') or img_elem.get('data-src')
                if img_src:
                    if img_src.startswith('/'):
                        img_src = self.base_url + img_src
                    offer_data['image_url'] = img_src
            
            # Extract terms
            terms_elem = container.find(['div', 'p'], class_=['terms', 'conditions', 'fine-print'])
            if terms_elem:
                offer_data['terms'] = terms_elem.get_text(strip=True)
            
            # Look for minimum spend information
            text_content = container.get_text()
            if 'minimum spend' in text_content.lower() or 'min purchase' in text_content.lower():
                for elem in container.find_all(['span', 'div', 'p']):
                    elem_text = elem.get_text()
                    if 'minimum' in elem_text.lower() and ('rs' in elem_text.lower() or 'lkr' in elem_text.lower()):
                        offer_data['minimum_spend'] = elem_text.strip()
                        break
            
            # Validate minimum required data
            if not offer_data.get('merchant') and not offer_data.get('description'):
                return None
            
            # Set defaults for missing required fields
            if not offer_data.get('merchant'):
                offer_data['merchant'] = "Sampath Bank Partner"
            
            if not offer_data.get('description'):
                offer_data['description'] = f"Special offer for Sampath Bank customers"
            
            if not offer_data.get('discount'):
                offer_data['discount'] = "Special Offer"
            
            return offer_data
            
        except Exception as e:
            logger.error(f"Error extracting Sampath Bank offer data: {e}")
            return None
