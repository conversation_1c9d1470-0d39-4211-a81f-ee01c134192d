"""
Base scraper class for all bank scrapers
"""
import time
import random
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime

import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential

from config import settings
from models import BankOffer, ScrapingResult
from utils import (
    get_random_user_agent,
    add_random_delay,
    clean_text,
    categorize_offer,
    parse_date,
    extract_discount_percentage,
    extract_amount,
    validate_offer_data
)


class BaseScraper(ABC):
    """Base class for all bank scrapers"""
    
    def __init__(self, bank_name: str):
        self.bank_name = bank_name
        self.session = requests.Session()
        self.driver: Optional[webdriver.Chrome] = None
        self.setup_session()
    
    def setup_session(self):
        """Setup requests session with headers"""
        self.session.headers.update({
            'User-Agent': get_random_user_agent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def setup_selenium(self) -> bool:
        """Setup Selenium WebDriver"""
        try:
            chrome_options = Options()
            
            if settings.HEADLESS_BROWSER:
                chrome_options.add_argument('--headless')
            
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument(f'--user-agent={get_random_user_agent()}')
            
            # Install and setup ChromeDriver
            driver_path = ChromeDriverManager().install()
            self.driver = webdriver.Chrome(driver_path, options=chrome_options)
            self.driver.set_page_load_timeout(settings.BROWSER_TIMEOUT)
            
            logger.info(f"Selenium WebDriver setup completed for {self.bank_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup Selenium for {self.bank_name}: {e}")
            return False
    
    def cleanup_selenium(self):
        """Cleanup Selenium WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info(f"Selenium WebDriver cleaned up for {self.bank_name}")
            except Exception as e:
                logger.error(f"Error cleaning up Selenium: {e}")
            finally:
                self.driver = None
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def make_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
        """Make HTTP request with retry logic"""
        try:
            add_random_delay()
            
            if method.upper() == 'GET':
                response = self.session.get(url, timeout=settings.REQUEST_TIMEOUT, **kwargs)
            elif method.upper() == 'POST':
                response = self.session.post(url, timeout=settings.REQUEST_TIMEOUT, **kwargs)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            logger.debug(f"Successfully fetched: {url}")
            return response
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed for {url}: {e}")
            raise
    
    def parse_html(self, html_content: str) -> BeautifulSoup:
        """Parse HTML content with BeautifulSoup"""
        return BeautifulSoup(html_content, 'lxml')
    
    def create_offer(self, offer_data: Dict[str, Any]) -> Optional[BankOffer]:
        """Create BankOffer object from scraped data"""
        try:
            # Validate required data
            if not validate_offer_data(offer_data):
                return None
            
            # Clean and process data
            processed_data = {
                'bank': self.bank_name,
                'merchant': clean_text(offer_data.get('merchant', '')),
                'discount': clean_text(offer_data.get('discount', '')),
                'description': clean_text(offer_data.get('description', '')),
                'valid_until': parse_date(offer_data.get('valid_until', '')) or 'Not specified',
                'category': offer_data.get('category') or categorize_offer(
                    offer_data.get('merchant', ''),
                    offer_data.get('description', '')
                ),
                'image_url': offer_data.get('image_url'),
                'is_active': True,
                'terms': clean_text(offer_data.get('terms', '')),
                'minimum_spend': extract_amount(offer_data.get('minimum_spend', '')) if offer_data.get('minimum_spend') else None,
                'maximum_discount': extract_amount(offer_data.get('maximum_discount', '')) if offer_data.get('maximum_discount') else None
            }
            
            # Extract discount if not provided
            if not processed_data['discount']:
                processed_data['discount'] = extract_discount_percentage(processed_data['description']) or 'Special Offer'
            
            return BankOffer(**processed_data)
            
        except Exception as e:
            logger.error(f"Failed to create offer object: {e}")
            logger.error(f"Offer data: {offer_data}")
            return None
    
    @abstractmethod
    def scrape_offers(self) -> ScrapingResult:
        """
        Abstract method to scrape offers from the bank's website
        Must be implemented by each bank scraper
        """
        pass
    
    def run(self) -> ScrapingResult:
        """Run the scraper"""
        logger.info(f"Starting scraper for {self.bank_name}")
        start_time = datetime.now()
        
        try:
            result = self.scrape_offers()
            
            # Calculate scraping time
            end_time = datetime.now()
            scraping_duration = (end_time - start_time).total_seconds()
            
            logger.info(f"Scraping completed for {self.bank_name} in {scraping_duration:.2f} seconds")
            logger.info(f"Found {result.offers_count} offers")
            
            return result
            
        except Exception as e:
            logger.error(f"Scraping failed for {self.bank_name}: {e}")
            return ScrapingResult(
                bank=self.bank_name,
                success=False,
                error_message=str(e)
            )
        
        finally:
            self.cleanup_selenium()
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.cleanup_selenium()


class WebScrapingScraper(BaseScraper):
    """Base class for scrapers that use web scraping"""
    
    def scrape_with_requests(self, url: str) -> Optional[BeautifulSoup]:
        """Scrape page using requests"""
        try:
            response = self.make_request(url)
            if response:
                return self.parse_html(response.text)
        except Exception as e:
            logger.error(f"Failed to scrape {url} with requests: {e}")
        return None
    
    def scrape_with_selenium(self, url: str) -> Optional[BeautifulSoup]:
        """Scrape page using Selenium"""
        try:
            if not self.driver and not self.setup_selenium():
                return None
            
            self.driver.get(url)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Add delay to let dynamic content load
            time.sleep(2)
            
            html_content = self.driver.page_source
            return self.parse_html(html_content)
            
        except (TimeoutException, WebDriverException) as e:
            logger.error(f"Failed to scrape {url} with Selenium: {e}")
        return None


class APIScraper(BaseScraper):
    """Base class for scrapers that use APIs"""
    
    def make_api_request(self, url: str, headers: Optional[Dict] = None, params: Optional[Dict] = None) -> Optional[Dict]:
        """Make API request and return JSON response"""
        try:
            request_headers = self.session.headers.copy()
            if headers:
                request_headers.update(headers)
            
            response = self.make_request(url, headers=request_headers, params=params)
            if response:
                return response.json()
                
        except Exception as e:
            logger.error(f"API request failed for {url}: {e}")
        return None
