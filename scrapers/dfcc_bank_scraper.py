"""
DFCC Bank scraper
"""
from typing import List, Dict, Any
from loguru import logger

from .base_scraper import APIScraper, WebScrapingScraper
from models import ScrapingResult, BankOffer


class DFCCBankScraper(WebScrapingScraper):
    """Scraper for DFCC Bank offers"""
    
    def __init__(self):
        super().__init__("DFCC Bank")
        self.base_url = "https://www.dfcc.lk"
        self.offers_url = f"{self.base_url}/offers"
        self.api_url = f"{self.base_url}/api/offers"  # Example API endpoint
    
    def scrape_offers(self) -> ScrapingResult:
        """Scrape offers from DFCC Bank website"""
        offers = []
        
        try:
            # First try API approach
            api_offers = self.scrape_from_api()
            if api_offers:
                offers.extend(api_offers)
                logger.info(f"Retrieved {len(api_offers)} offers from API")
            
            # If API fails or returns no data, try web scraping
            if not offers:
                web_offers = self.scrape_from_web()
                if web_offers:
                    offers.extend(web_offers)
                    logger.info(f"Retrieved {len(web_offers)} offers from web scraping")
            
            return ScrapingResult(
                bank=self.bank_name,
                success=True,
                offers_count=len(offers),
                offers=offers
            )
            
        except Exception as e:
            logger.error(f"Error scraping DFCC Bank offers: {e}")
            return ScrapingResult(
                bank=self.bank_name,
                success=False,
                error_message=str(e)
            )
    
    def scrape_from_api(self) -> List[BankOffer]:
        """Try to scrape offers from API"""
        offers = []
        
        try:
            # Try to make API request
            api_data = self.make_api_request(self.api_url)
            
            if api_data and isinstance(api_data, dict):
                # Process API response
                offers_data = api_data.get('offers', [])
                if not offers_data and 'data' in api_data:
                    offers_data = api_data['data']
                
                for offer_item in offers_data:
                    offer_data = self.process_api_offer(offer_item)
                    if offer_data:
                        offer = self.create_offer(offer_data)
                        if offer:
                            offers.append(offer)
            
        except Exception as e:
            logger.warning(f"API scraping failed for DFCC Bank: {e}")
        
        return offers
    
    def scrape_from_web(self) -> List[BankOffer]:
        """Scrape offers from website"""
        offers = []
        
        try:
            soup = self.scrape_with_requests(self.offers_url)
            if not soup:
                return offers
            
            # Find offer containers
            offer_containers = soup.find_all('div', class_=['offer-card', 'promotion', 'deal-card'])
            
            if not offer_containers:
                # Try alternative selectors
                offer_containers = soup.find_all('div', class_=['card', 'item'])
                offer_containers = [c for c in offer_containers if self.is_offer_container(c)]
            
            for container in offer_containers:
                offer_data = self.extract_web_offer_data(container)
                if offer_data:
                    offer = self.create_offer(offer_data)
                    if offer:
                        offers.append(offer)
        
        except Exception as e:
            logger.error(f"Web scraping failed for DFCC Bank: {e}")
        
        return offers
    
    def process_api_offer(self, api_offer: Dict[str, Any]) -> Dict[str, Any]:
        """Process offer data from API response"""
        try:
            offer_data = {
                'merchant': api_offer.get('merchant_name') or api_offer.get('title') or api_offer.get('name'),
                'discount': api_offer.get('discount_text') or api_offer.get('offer_text'),
                'description': api_offer.get('description') or api_offer.get('details'),
                'valid_until': api_offer.get('valid_until') or api_offer.get('expiry_date'),
                'category': api_offer.get('category'),
                'image_url': api_offer.get('image_url') or api_offer.get('image'),
                'terms': api_offer.get('terms_and_conditions') or api_offer.get('terms'),
                'minimum_spend': api_offer.get('minimum_spend'),
                'maximum_discount': api_offer.get('maximum_discount')
            }
            
            # Clean up None values
            offer_data = {k: v for k, v in offer_data.items() if v is not None}
            
            return offer_data
            
        except Exception as e:
            logger.error(f"Error processing API offer: {e}")
            return None
    
    def is_offer_container(self, container) -> bool:
        """Check if container contains offer information"""
        text = container.get_text().lower()
        offer_keywords = ['offer', 'discount', 'promotion', '%', 'off', 'deal', 'save', 'cashback']
        return any(keyword in text for keyword in offer_keywords)
    
    def extract_web_offer_data(self, container) -> Dict[str, Any]:
        """Extract offer data from web page container"""
        try:
            offer_data = {}
            
            # Extract title/merchant
            title_elem = container.find(['h1', 'h2', 'h3', 'h4', 'h5'])
            if title_elem:
                offer_data['merchant'] = title_elem.get_text(strip=True)
            
            # Extract discount
            discount_elem = container.find(['span', 'div'], class_=['discount', 'percentage', 'offer'])
            if not discount_elem:
                # Look for elements with % or "off"
                for elem in container.find_all(['span', 'div', 'strong']):
                    text = elem.get_text()
                    if '%' in text or 'off' in text.lower():
                        discount_elem = elem
                        break
            
            if discount_elem:
                offer_data['discount'] = discount_elem.get_text(strip=True)
            
            # Extract description
            desc_elem = container.find(['p', 'div'], class_=['description', 'content'])
            if desc_elem:
                offer_data['description'] = desc_elem.get_text(strip=True)
            else:
                # Use container text as description
                text = container.get_text(strip=True)
                if offer_data.get('merchant'):
                    text = text.replace(offer_data['merchant'], '').strip()
                offer_data['description'] = text[:250] + "..." if len(text) > 250 else text
            
            # Extract validity
            for elem in container.find_all(['span', 'div', 'p']):
                text = elem.get_text().lower()
                if any(word in text for word in ['valid', 'expires', 'until', 'till']):
                    offer_data['valid_until'] = elem.get_text(strip=True)
                    break
            
            # Extract image
            img_elem = container.find('img')
            if img_elem and img_elem.get('src'):
                img_url = img_elem['src']
                if img_url.startswith('/'):
                    img_url = self.base_url + img_url
                offer_data['image_url'] = img_url
            
            # Set defaults if missing
            if not offer_data.get('merchant'):
                offer_data['merchant'] = "DFCC Bank Partner"
            
            if not offer_data.get('description'):
                offer_data['description'] = "Special offer for DFCC Bank customers"
            
            if not offer_data.get('discount'):
                offer_data['discount'] = "Special Offer"
            
            return offer_data
            
        except Exception as e:
            logger.error(f"Error extracting DFCC Bank web offer data: {e}")
            return None
