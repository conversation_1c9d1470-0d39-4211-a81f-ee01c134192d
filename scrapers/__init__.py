"""
Scrapers package
"""
from .base_scraper import BaseScraper, WebScrapingScraper, APIScraper
from .commercial_bank_scraper import CommercialBankScraper
from .sampath_bank_scraper import SampathBankScraper
from .dfcc_bank_scraper import DFCCBankScraper
from .template_scraper import (
    BOCScraper,
    PeoplesBankScraper,
    NTBScraper,
    NDBScraper,
    HSBCScraper,
    SeylanBankScraper,
    HNBScraper,
    UnionBankScraper,
    CargillsBankScraper,
    PanAsiaBankScraper,
    LOLCScraper
)

# Scraper registry
SCRAPERS = {
    "Commercial Bank": CommercialBankScraper,
    "Sampath Bank": SampathBankScraper,
    "DFCC Bank": DFCCBankScraper,
    "BOC": BOCScraper,
    "People's Bank": PeoplesBankScraper,
    "NTB": NTBScraper,
    "NDB": NDBScraper,
    "HSBC": <PERSON><PERSON>CScrap<PERSON>,
    "Seylan Bank": SeylanBankScraper,
    "HNB": HNBScraper,
    "Union Bank": UnionBankScraper,
    "Cargills Bank": CargillsBankScraper,
    "Pan Asia Bank": PanAsiaBankScraper,
    "LOLC": LOLCScraper,
}


def get_scraper(bank_name: str):
    """Get scraper instance for a bank"""
    scraper_class = SCRAPERS.get(bank_name)
    if scraper_class:
        return scraper_class()
    else:
        raise ValueError(f"No scraper available for {bank_name}")


def get_available_banks():
    """Get list of banks with available scrapers"""
    return list(SCRAPERS.keys())


__all__ = [
    "BaseScraper",
    "WebScrapingScraper",
    "APIScraper",
    "CommercialBankScraper",
    "SampathBankScraper",
    "DFCCBankScraper",
    "BOCScraper",
    "PeoplesBankScraper",
    "NTBScraper",
    "NDBScraper",
    "HSBCScraper",
    "SeylanBankScraper",
    "HNBScraper",
    "UnionBankScraper",
    "CargillsBankScraper",
    "PanAsiaBankScraper",
    "LOLCScraper",
    "SCRAPERS",
    "get_scraper",
    "get_available_banks"
]
