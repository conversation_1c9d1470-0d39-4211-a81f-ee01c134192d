version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: bank_offers_mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: bank_offers
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - bank_offers_network

  scraper:
    build: .
    container_name: bank_offers_scraper
    restart: unless-stopped
    depends_on:
      - mongodb
    environment:
      MONGODB_URL: *******************************************************************
      DATABASE_NAME: bank_offers
      COLLECTION_NAME: offers
      HEADLESS_BROWSER: "true"
      LOG_LEVEL: INFO
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - bank_offers_network
    # Run scraper every hour
    command: >
      sh -c "
        while true; do
          python main.py --all
          echo 'Scraping completed. Waiting 1 hour...'
          sleep 3600
        done
      "

volumes:
  mongodb_data:

networks:
  bank_offers_network:
    driver: bridge
