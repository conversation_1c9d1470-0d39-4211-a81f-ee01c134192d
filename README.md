# Bank Offer Scraper

A comprehensive Python application for scraping credit card offers from various Sri Lankan banks and storing them in MongoDB.

## Features

- **Multi-Bank Support**: Scrapes offers from 14+ Sri Lankan banks
- **Flexible Scraping**: Supports both web scraping and API-based data collection
- **Database Integration**: Stores offers in MongoDB with proper data validation
- **Robust Error Handling**: Retry mechanisms and comprehensive logging
- **Modular Architecture**: Easy to add new bank scrapers
- **Data Validation**: Ensures data quality and consistency
- **Automatic Categorization**: Categorizes offers based on merchant and description

## Supported Banks

- BOC (Bank of Ceylon)
- DFCC Bank
- People's Bank
- Commercial Bank
- Sampath Bank
- NTB (Nations Trust Bank)
- NDB (National Development Bank)
- HSBC
- Seylan Bank
- HNB (Hatton National Bank)
- Union Bank
- Cargills Bank
- Pan Asia Bank
- LOLC

## Project Structure

```
bank-promo-scan/
├── config/
│   ├── __init__.py
│   └── settings.py          # Configuration settings
├── models/
│   ├── __init__.py
│   └── offer.py             # Data models
├── scrapers/
│   ├── __init__.py
│   ├── base_scraper.py      # Base scraper classes
│   ├── commercial_bank_scraper.py
│   ├── sampath_bank_scraper.py
│   ├── dfcc_bank_scraper.py
│   └── ...                  # Other bank scrapers
├── utils/
│   ├── __init__.py
│   ├── database.py          # Database utilities
│   ├── logger.py            # Logging configuration
│   └── helpers.py           # Helper functions
├── logs/                    # Log files
├── main.py                  # Main entry point
├── requirements.txt         # Dependencies
├── .env.example            # Environment variables template
└── README.md
```

## Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/lahiru-sandanuwan/bank-promo-scan.git
   cd bank-promo-scan
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Setup environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Install Chrome WebDriver** (for Selenium):
   - The application will automatically download ChromeDriver using webdriver-manager
   - Ensure Google Chrome is installed on your system

## Configuration

Edit the `.env` file with your settings:

```env
# Database Configuration
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=bank_offers
COLLECTION_NAME=offers

# Scraping Configuration
REQUEST_TIMEOUT=30
MAX_RETRIES=3
DELAY_BETWEEN_REQUESTS=1.0

# Browser Configuration
HEADLESS_BROWSER=True
BROWSER_TIMEOUT=30

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/scraper.log
```

## Usage

### Command Line Interface

1. **List available banks**:
   ```bash
   python main.py --list-banks
   ```

2. **Scrape all banks**:
   ```bash
   python main.py --all
   ```

3. **Scrape specific bank**:
   ```bash
   python main.py --bank "Commercial Bank"
   ```

4. **Default (scrape all)**:
   ```bash
   python main.py
   ```

### Programmatic Usage

```python
from scrapers import get_scraper
from utils import db_manager

# Setup database
db_manager.connect()

# Scrape specific bank
scraper = get_scraper("Commercial Bank")
result = scraper.run()

if result.success:
    # Save to database
    db_manager.insert_offers(result.offers)
    print(f"Found {result.offers_count} offers")
else:
    print(f"Scraping failed: {result.error_message}")

# Cleanup
db_manager.disconnect()
```

## Data Model

Each offer is stored with the following structure:

```json
{
    "_id": "ObjectId",
    "bank": "Commercial Bank",
    "merchant": "Hilton Colombo",
    "discount": "20% OFF",
    "description": "20% off on total bill for Commercial Bank credit card holders",
    "validUntil": "31 Dec 2025",
    "category": "Travel & Leisure",
    "imageUrl": "https://example.com/hilton-colombo.jpg",
    "isActive": true,
    "terms": "",
    "minimumSpend": 5000,
    "maximumDiscount": 10000,
    "createdAt": "2025-05-26T15:25:38.624Z",
    "updatedAt": "2025-05-26T15:25:38.624Z"
}
```

## Adding New Bank Scrapers

1. **Create new scraper file**:
   ```python
   # scrapers/new_bank_scraper.py
   from .base_scraper import WebScrapingScraper
   from models import ScrapingResult
   
   class NewBankScraper(WebScrapingScraper):
       def __init__(self):
           super().__init__("New Bank")
           self.base_url = "https://www.newbank.lk"
           self.offers_url = f"{self.base_url}/offers"
       
       def scrape_offers(self) -> ScrapingResult:
           # Implement scraping logic
           pass
   ```

2. **Register in scrapers/__init__.py**:
   ```python
   from .new_bank_scraper import NewBankScraper
   
   SCRAPERS = {
       # ... existing scrapers
       "New Bank": NewBankScraper,
   }
   ```

## Logging

The application provides comprehensive logging:

- **Console output**: Colored logs for development
- **File logging**: Rotating log files in `logs/` directory
- **Log levels**: DEBUG, INFO, WARNING, ERROR
- **Structured logging**: Includes timestamps, function names, and line numbers

## Error Handling

- **Retry mechanisms**: Automatic retries for failed requests
- **Graceful degradation**: Falls back to alternative scraping methods
- **Comprehensive error logging**: Detailed error messages and stack traces
- **Data validation**: Ensures data quality before database insertion

## Performance Considerations

- **Rate limiting**: Configurable delays between requests
- **Concurrent scraping**: Can be extended for parallel processing
- **Efficient selectors**: Optimized CSS selectors for faster parsing
- **Memory management**: Proper cleanup of browser instances

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add your bank scraper following the existing patterns
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This tool is for educational and research purposes. Please respect the terms of service of the websites you scrape and implement appropriate rate limiting to avoid overloading servers.
