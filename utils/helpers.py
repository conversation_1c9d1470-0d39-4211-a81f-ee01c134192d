"""
Helper utilities for scraping
"""
import re
import random
import time
from datetime import datetime, timedelta
from typing import Optional, List
from urllib.parse import urljoin, urlparse
from loguru import logger

from config import settings


def clean_text(text: str) -> str:
    """Clean and normalize text"""
    if not text:
        return ""
    
    # Remove extra whitespace and newlines
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove special characters but keep basic punctuation
    text = re.sub(r'[^\w\s\-.,!?%&():/]', '', text)
    
    return text


def extract_discount_percentage(text: str) -> Optional[str]:
    """Extract discount percentage from text"""
    if not text:
        return None
    
    # Look for patterns like "20%", "20% OFF", "Up to 20%"
    patterns = [
        r'(\d+)%\s*(?:OFF|off)?',
        r'(?:Up to|up to)\s*(\d+)%',
        r'(\d+)\s*percent'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            percentage = match.group(1)
            return f"{percentage}% OFF"
    
    return None


def extract_amount(text: str) -> Optional[float]:
    """Extract monetary amount from text"""
    if not text:
        return None
    
    # Look for patterns like "Rs. 5000", "LKR 5,000", "5000"
    patterns = [
        r'(?:Rs\.?\s*|LKR\s*)?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        r'(\d+(?:\.\d{2})?)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text.replace(',', ''))
        if match:
            try:
                return float(match.group(1).replace(',', ''))
            except ValueError:
                continue
    
    return None


def parse_date(date_str: str) -> Optional[str]:
    """Parse various date formats and return standardized format"""
    if not date_str:
        return None
    
    # Common date patterns
    patterns = [
        r'(\d{1,2})\s*(?:st|nd|rd|th)?\s*([A-Za-z]+)\s*(\d{4})',  # 31st Dec 2025
        r'(\d{1,2})[/-](\d{1,2})[/-](\d{4})',  # 31/12/2025 or 31-12-2025
        r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})',  # 2025/12/31 or 2025-12-31
        r'([A-Za-z]+)\s*(\d{1,2}),?\s*(\d{4})',  # December 31, 2025
    ]
    
    months = {
        'jan': '01', 'january': '01',
        'feb': '02', 'february': '02',
        'mar': '03', 'march': '03',
        'apr': '04', 'april': '04',
        'may': '05',
        'jun': '06', 'june': '06',
        'jul': '07', 'july': '07',
        'aug': '08', 'august': '08',
        'sep': '09', 'september': '09',
        'oct': '10', 'october': '10',
        'nov': '11', 'november': '11',
        'dec': '12', 'december': '12'
    }
    
    date_str = date_str.lower().strip()
    
    for pattern in patterns:
        match = re.search(pattern, date_str)
        if match:
            try:
                if 'jan' in date_str or 'feb' in date_str or any(month in date_str for month in months.keys()):
                    # Month name format
                    if len(match.groups()) == 3:
                        day, month, year = match.groups()
                        if month.lower() in months:
                            month_num = months[month.lower()]
                            return f"{day.zfill(2)} {month.title()} {year}"
                else:
                    # Numeric format
                    parts = match.groups()
                    if len(parts) == 3:
                        if len(parts[0]) == 4:  # Year first
                            year, month, day = parts
                        else:  # Day first
                            day, month, year = parts
                        return f"{day.zfill(2)} {datetime(int(year), int(month), 1).strftime('%b')} {year}"
            except (ValueError, IndexError):
                continue
    
    return date_str


def categorize_offer(merchant: str, description: str) -> str:
    """Categorize offer based on merchant and description"""
    text = f"{merchant} {description}".lower()
    
    categories = {
        "Travel & Leisure": ["hotel", "travel", "airline", "resort", "vacation", "tour", "spa", "leisure"],
        "Dining": ["restaurant", "cafe", "food", "dining", "meal", "pizza", "burger", "coffee"],
        "Shopping": ["mall", "shop", "store", "retail", "fashion", "clothing", "shoes"],
        "Fuel": ["fuel", "petrol", "gas", "station", "ceypetco", "ioc"],
        "Utilities": ["utility", "bill", "electricity", "water", "telephone", "internet"],
        "Healthcare": ["hospital", "clinic", "pharmacy", "medical", "health", "doctor"],
        "Entertainment": ["cinema", "movie", "theater", "entertainment", "game", "sport"],
        "Online Shopping": ["online", "e-commerce", "website", "app", "digital"],
        "Grocery": ["supermarket", "grocery", "food city", "keells", "arpico"],
        "Electronics": ["electronics", "mobile", "computer", "laptop", "tv", "phone"]
    }
    
    for category, keywords in categories.items():
        if any(keyword in text for keyword in keywords):
            return category
    
    return "Other"


def get_random_user_agent() -> str:
    """Get a random user agent"""
    return random.choice(settings.USER_AGENTS)


def add_random_delay(min_delay: float = 0.5, max_delay: float = 2.0):
    """Add random delay between requests"""
    delay = random.uniform(min_delay, max_delay)
    time.sleep(delay)


def is_valid_url(url: str) -> bool:
    """Check if URL is valid"""
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def make_absolute_url(base_url: str, relative_url: str) -> str:
    """Convert relative URL to absolute URL"""
    if is_valid_url(relative_url):
        return relative_url
    return urljoin(base_url, relative_url)


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for saving"""
    # Remove invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove extra spaces and dots
    filename = re.sub(r'\.+', '.', filename)
    filename = re.sub(r'\s+', '_', filename)
    return filename.strip('._')


def log_scraping_stats(bank: str, total_offers: int, new_offers: int, errors: int):
    """Log scraping statistics"""
    logger.info(f"Scraping completed for {bank}")
    logger.info(f"  Total offers found: {total_offers}")
    logger.info(f"  New offers added: {new_offers}")
    logger.info(f"  Errors encountered: {errors}")


def validate_offer_data(offer_data: dict) -> bool:
    """Validate offer data before saving"""
    required_fields = ['bank', 'merchant', 'discount', 'description']
    
    for field in required_fields:
        if field not in offer_data or not offer_data[field]:
            logger.warning(f"Missing required field: {field}")
            return False
    
    return True
