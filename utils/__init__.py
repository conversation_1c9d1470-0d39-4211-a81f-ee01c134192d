"""
Utilities package
"""
from .database import db_manager
from .logger import setup_logger
from .helpers import (
    clean_text,
    extract_discount_percentage,
    extract_amount,
    parse_date,
    categorize_offer,
    get_random_user_agent,
    add_random_delay,
    is_valid_url,
    make_absolute_url,
    sanitize_filename,
    log_scraping_stats,
    validate_offer_data
)

__all__ = [
    "db_manager",
    "setup_logger",
    "clean_text",
    "extract_discount_percentage",
    "extract_amount",
    "parse_date",
    "categorize_offer",
    "get_random_user_agent",
    "add_random_delay",
    "is_valid_url",
    "make_absolute_url",
    "sanitize_filename",
    "log_scraping_stats",
    "validate_offer_data"
]
