"""
Database utilities for MongoDB operations
"""
import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime
from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.database import Database
from loguru import logger

from config import settings
from models import BankOffer


class DatabaseManager:
    """MongoDB database manager"""
    
    def __init__(self):
        self.client: Optional[MongoClient] = None
        self.database: Optional[Database] = None
        self.collection: Optional[Collection] = None
    
    def connect(self) -> bool:
        """Connect to MongoDB"""
        try:
            self.client = MongoClient(settings.MONGODB_URL)
            self.database = self.client[settings.DATABASE_NAME]
            self.collection = self.database[settings.COLLECTION_NAME]
            
            # Test connection
            self.client.admin.command('ping')
            logger.info(f"Connected to MongoDB: {settings.DATABASE_NAME}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    def insert_offer(self, offer: BankOffer) -> Optional[str]:
        """Insert a single offer"""
        try:
            if not self.collection:
                raise Exception("Database not connected")
            
            offer_dict = offer.to_dict()
            result = self.collection.insert_one(offer_dict)
            logger.info(f"Inserted offer: {offer.bank} - {offer.merchant}")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"Failed to insert offer: {e}")
            return None
    
    def insert_offers(self, offers: List[BankOffer]) -> int:
        """Insert multiple offers"""
        try:
            if not self.collection:
                raise Exception("Database not connected")
            
            if not offers:
                return 0
            
            offer_dicts = [offer.to_dict() for offer in offers]
            result = self.collection.insert_many(offer_dicts)
            count = len(result.inserted_ids)
            logger.info(f"Inserted {count} offers")
            return count
            
        except Exception as e:
            logger.error(f"Failed to insert offers: {e}")
            return 0
    
    def update_offer(self, offer_id: str, update_data: Dict[str, Any]) -> bool:
        """Update an existing offer"""
        try:
            if not self.collection:
                raise Exception("Database not connected")
            
            update_data["updatedAt"] = datetime.utcnow()
            result = self.collection.update_one(
                {"_id": offer_id},
                {"$set": update_data}
            )
            
            if result.modified_count > 0:
                logger.info(f"Updated offer: {offer_id}")
                return True
            else:
                logger.warning(f"No offer found with ID: {offer_id}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to update offer: {e}")
            return False
    
    def deactivate_old_offers(self, bank: str, current_offers: List[BankOffer]) -> int:
        """Deactivate offers that are no longer available"""
        try:
            if not self.collection:
                raise Exception("Database not connected")
            
            # Get current offer identifiers (bank + merchant + description)
            current_identifiers = set()
            for offer in current_offers:
                identifier = f"{offer.bank}_{offer.merchant}_{offer.description}"
                current_identifiers.add(identifier)
            
            # Find all active offers for this bank
            existing_offers = self.collection.find({
                "bank": bank,
                "isActive": True
            })
            
            deactivated_count = 0
            for existing_offer in existing_offers:
                identifier = f"{existing_offer['bank']}_{existing_offer['merchant']}_{existing_offer['description']}"
                
                if identifier not in current_identifiers:
                    # This offer is no longer available, deactivate it
                    self.collection.update_one(
                        {"_id": existing_offer["_id"]},
                        {
                            "$set": {
                                "isActive": False,
                                "updatedAt": datetime.utcnow()
                            }
                        }
                    )
                    deactivated_count += 1
            
            if deactivated_count > 0:
                logger.info(f"Deactivated {deactivated_count} old offers for {bank}")
            
            return deactivated_count
            
        except Exception as e:
            logger.error(f"Failed to deactivate old offers: {e}")
            return 0
    
    def get_offers_by_bank(self, bank: str, active_only: bool = True) -> List[Dict[str, Any]]:
        """Get all offers for a specific bank"""
        try:
            if not self.collection:
                raise Exception("Database not connected")
            
            query = {"bank": bank}
            if active_only:
                query["isActive"] = True
            
            offers = list(self.collection.find(query))
            return offers
            
        except Exception as e:
            logger.error(f"Failed to get offers for {bank}: {e}")
            return []
    
    def check_offer_exists(self, bank: str, merchant: str, description: str) -> bool:
        """Check if an offer already exists"""
        try:
            if not self.collection:
                raise Exception("Database not connected")
            
            existing = self.collection.find_one({
                "bank": bank,
                "merchant": merchant,
                "description": description,
                "isActive": True
            })
            
            return existing is not None
            
        except Exception as e:
            logger.error(f"Failed to check offer existence: {e}")
            return False


# Global database manager instance
db_manager = DatabaseManager()
